#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import collections
import copy

import netaddr
from neutron_lib.callbacks import events as callbacks_events
from neutron_lib.callbacks import registry as callbacks_registry
from neutron_lib.callbacks import resources as callbacks_resources
from neutron_lib import constants
from oslo_config import cfg
from oslo_log import log as logging
from oslo_utils import netutils

from neutron.agent.common import ovs_lib
from neutron.agent import firewall
from neutron.agent.linux.openvswitch_firewall import constants as ovsfw_consts
from neutron.agent.linux.openvswitch_firewall import exceptions
from neutron.agent.linux.openvswitch_firewall import firewall as ovs_fw
from neutron.agent.linux.openvswitch_firewall import iptables
from neutron.common import utils as neutron_utils
from neutron.plugins.ml2.drivers.openvswitch.agent.common import (constants
    as ovs_constants)
from neutron.plugins.ml2.drivers.openvswitch.agent import \
    ovs_agent_extension_api


LOG = logging.getLogger(__name__)

# OpenFlow Table IDs
OF_ZERO_TABLE = ovs_constants.LOCAL_SWITCHING
OF_TRANSIENT_TABLE = ovs_constants.TRANSIENT_TABLE
DVR_PRE_QOS_TABLE = ovs_constants.DVR_PRE_QOS_TABLE
TRAFFIC_MIRROR_TABLE = ovs_constants.TRAFFIC_MIRROR

OF_SELECT_TABLE = ovs_constants.BASE_EGRESS_TABLE
OF_EGRESS_TABLE = ovs_constants.RULES_EGRESS_TABLE
OF_NO_SG_VLAN_MATCH = ovs_constants.ACCEPT_OR_INGRESS_TABLE
OF_ACCEPT_OR_INGRESS_TABLE = ovs_constants.BASE_INGRESS_TABLE
OF_SEC_EXT_TABLE = ovs_constants.RULES_INGRESS_TABLE

OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE = (
    ovs_constants.ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE)


# Openflow ZERO table priorities
OF_T0_ARP_INT_PRIO = 100
OF_T0_ARP_EXT_PRIO = 90
OF_T0_ARP_NO_SG_PRIO = 91
OF_T0_SELECT_TABLE_IN_PRIO = 50
OF_T0_SELECT_TABLE_EXT_PRIO = 40
OF_T0_BLOCK = 35
OF_T0_NO_SG = 36
OF_TO_ICMPV6_INT_PRIO = 100
OF_TRAFFIC_ARP_FORBIDDEN_PRI = 220

# Openflow SELECT table priorities
OF_SEL_SERVICES_INT_IGMP_PRIO = 200
OF_SEL_SERVICES_EXT_IGMP_PRIO = 190
OF_SEL_SERVICES_EXT_MULTICAST_PRIO = 180
OF_SEL_EGRESS_PRIO = 100
OF_SEL_EGRESS_ALLOW_PRIO = 120
OF_SEL_EGRESS_DROP_PRIO = 110
OF_SEL_INGRESS_PRIO = 100
OF_DROP_TRAFFIC_PRIO = 50

# Openflow EGRESS table priorities
OF_EGRESS_SERVICES_PRIO = 2020
OF_EGRESS_ANTISPOOF_PRIO = 2010
OF_EGRESS_PORT_RULE_PRIO = 1000
OF_EGRESS_ALLOW_EGRESS_RULE_PRIO = 20

# Openflow INGRESS table priorities
OF_INGRESS_SERVICES_PRIO = 2020
OF_INGRESS_ANTISPOOF_PRIO = 2010
OF_INGRESS_PORT_RULE_PRIO = 1000
OF_INGRESS_OUTBOUND_PRIO = 10
PRIORITY_GAP = 5

# Openflow SEC_EXT table priorities
OF_SEC_EXT_BLOCK_INT_MAC_PRIO = 100
OF_SEC_EXT_BLOCK_EXT_SOURCE_PRIO = 100
OF_SEC_EXT_BLOCK_MULTICAST_PRIO = 100
OF_SEC_EXT_DHCP_PRIO = 100
OF_SEC_EXT_ALLOW_EXT_TRAFFIC_PRIO = 50

# Openflow LEARN ACTIONS priorities
OF_LEARNED_HIGH_PRIO = 3010
OF_LEARNED_LOW_PRIO = 3000

INGRESS_DIRECTION = 'ingress'
EGRESS_DIRECTION = 'egress'
LEARN_IDLE_TIMEOUT = 30  # 30 seconds.
LEARN_HARD_TIMEOUT = 1800  # 30 minutes.

# Ethernet type.
IPv4 = "IPv4"
IPv6 = "IPv6"

# OpenFlow mnemonics.
OF_MNEMONICS = {
    IPv6: {
        "ip_dst": "ipv6_dst",
        "ip_src": "ipv6_src",
        "ip_proto": "ipv6",
    },
    IPv4: {
        "ip_dst": "nw_dst",
        "ip_src": "nw_src",
        "ip_proto": "ip",
    },
}

# Protocols.
# https://datatracker.ietf.org/doc/html/rfc1256
IPV4_ROUTER_MESSAGES = [9, 10]
# Router Solicitation (133)
# Router Advertisement (134)
# Neighbor Solicitation (135)
# Neighbor Advertisement (136)
# Redirect (137)
ICMPV6_TYPE_RS = 133
ICMPV6_TYPE_RA = constants.ICMPV6_TYPE_RA or 134
ICMPv6_TYPE_NS = 135
ICMPV6_TYPE_NA = constants.ICMPV6_TYPE_NA or 136
ICMPV6_TYPE_RED = 137
IPV6_ND_MESSAGES = [ICMPV6_TYPE_RS, ICMPV6_TYPE_RA, ICMPv6_TYPE_NS,
                    ICMPV6_TYPE_NA, ICMPV6_TYPE_RED]
IPV6_ND_MESSAGES_NO_NS = [
    ICMPV6_TYPE_RA, ICMPV6_TYPE_NA, ICMPV6_TYPE_RED, ICMPV6_TYPE_RS]
# Multicast Listener Query (130)
# Multicast Listener Report (131)
# Multicast Listener Done (132)
# https://datatracker.ietf.org/doc/html/rfc3810
ICMPV6_TYPE_MLQ = 130
ICMPV6_TYPE_MLR = 131
ICMPV6_TYPE_MLD = 132
IPV6_MLD_MESSAGES = [ICMPV6_TYPE_MLQ, ICMPV6_TYPE_MLR, ICMPV6_TYPE_MLD]

IPv6_MULTICAST_PREFIX = "ff02::1:ff00:0/104"

DIRECTION_IP_PREFIX = {'ingress': 'source_ip_prefix',
                       'egress': 'dest_ip_prefix'}

ETH_PROTOCOL_TABLE = {IPv4: "0x0800",
                      IPv6: "0x86dd"}

IP_PROTOCOL_TABLE = {
    constants.PROTO_NAME_TCP: constants.PROTO_NUM_TCP,
    constants.PROTO_NAME_ICMP: constants.PROTO_NUM_ICMP,
    constants.PROTO_NAME_IPV6_ICMP: constants.PROTO_NUM_IPV6_ICMP,
    constants.PROTO_NAME_UDP: constants.PROTO_NUM_UDP,
    "igmp": 2,
    constants.PROTO_NAME_SCTP: constants.PROTO_NUM_SCTP}

MULTICAST_MAC = "01:00:5e:00:00:00/01:00:5e:00:00:00"

IPV4_MULTICAST = '*********/4'
IPV6_MULTICAST = 'ff00::/8'


class OVSStatelessFirewallDriver(firewall.FirewallDriver):
    """Driver which enforces security groups through
           Open vSwitch flows.
        """

    # provides_arp_spoofing_protection = True

    def __init__(self, integration_bridge):
        self._filtered_ports = {}
        self.unfiltered = {}
        self._int_br = ovs_agent_extension_api.\
            OVSCookieBridge(integration_bridge).deferred(full_ordered=True)
        self._deferred = False
        self.iptables_helper = iptables.Helper(self._int_br.br)
        self.iptables_helper.load_driver_if_needed()
        self._enable_multicast = \
            cfg.CONF.OVS.enable_sg_firewall_multicast

        # List of security group rules for ports residing on this host
        self.sg_rules = {}

        # List of security group member ips for ports residing on this
        # host
        self.sg_members = collections.defaultdict(
            lambda: collections.defaultdict(list))
        self.pre_sg_members = None

        # Known ports managed.
        self._filtered_in_ports = {}
        self._initialize_firewall()

        self.learn_cookie = self._int_br.br.request_cookie()
        self.learn_idle_timeout = cfg.CONF.OVS.firewall_learn_idle_timeout
        self.learn_hard_timeout = cfg.CONF.OVS.firewall_learn_hard_timeout

        callbacks_registry.subscribe(
            self._init_firewall_callback,
            callbacks_resources.AGENT,
            callbacks_events.OVS_RESTARTED)

    @property
    def ports(self):
        return self._filtered_ports

    def _init_firewall_callback(self, resource, event, trigger, **kwargs):
        LOG.info("Reinitialize Openvswitch stateless firewall after OVS "
                 "restart.")
        self._initialize_firewall()

    def _initialize_firewall(self):
        self._int_br.br.add_flow(
            table=OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE,
            priority=1,
            actions='normal')
        self._add_flow(
            table=OF_NO_SG_VLAN_MATCH,
            priority=1,
            actions='resubmit(,%d)' % OF_ACCEPT_OR_INGRESS_TABLE
        )
        self._int_br.br.add_flow(
            table=TRAFFIC_MIRROR_TABLE,
            priority=OF_T0_BLOCK,
            actions='resubmit(,%d),resubmit(,%d)' % (
                ovs_constants.DSCP_LEARN_TABLE, OF_SELECT_TABLE)
        )

    def _vif_port_info(self, port_name):
        """Returns additional vif port info: internal vlan tag,
            segmentation id, net id, network type, physical
            network.
            """
        port_info = {'name': port_name}
        other_config = self._int_br.br.db_get_val("Port", port_name,
                                                  "other_config")
        # Default fields (also other fields could be present):
        #   net_uuid="e00e6a6a-c88a-4724-80a7-6368a94241d9"
        #   network_type=vlan
        #   physical_network=default
        #   segmentation_id="1402"
        port_info.update(other_config)

        try:
            port_info['tag'] = int(other_config['tag'])
        except (KeyError, TypeError, ValueError):
            raise exceptions.OVSFWTagNotFound(
                port_name=port_name, other_config=other_config)

        return port_info

    def update_security_group_rules(self, sg_id, sg_rules):
        LOG.debug("Update rules of security group (%s)", sg_id)
        self.sg_rules[sg_id] = sg_rules

    def update_security_group_members(self, sg_id, sg_members):
        LOG.debug("Update members of security group (%s)", sg_id)
        self.sg_members[sg_id] = collections.defaultdict(list, sg_members)

    def security_group_updated(self, action_type, sec_group_ids,
                               device_ids=None):
        pass

    def _expand_sg_rule_with_remote_ips(self, rule, port):
        """Expand a remote group rule to rule per remote group IP."""
        remote_group_id = rule.get('remote_group_id')
        if remote_group_id:
            ethertype = rule['ethertype']
            port_ips = port.get('fixed_ips', [])

            for ip, _mac in self.sg_members[remote_group_id][ethertype]:
                if ip not in port_ips:
                    ip_rule = rule.copy()
                    direction_ip_prefix = (
                        DIRECTION_IP_PREFIX[rule['direction']])
                    ip_prefix = str(netaddr.IPNetwork(ip).cidr)
                    ip_rule[direction_ip_prefix] = ip_prefix
                    yield ip_rule
        else:
            yield rule

    def _write_proto(self, eth_type, protocol=None):
        if protocol == "arp":
            return "arp"

        proto_str = "eth_type=%s" % ETH_PROTOCOL_TABLE[eth_type]
        if protocol in IP_PROTOCOL_TABLE.keys():
            proto_num = IP_PROTOCOL_TABLE[protocol]
            if protocol == constants.PROTO_NAME_ICMP and \
                    eth_type == IPv6:
                proto_num = constants.PROTO_NUM_IPV6_ICMP
            proto_str += ",ip_proto=%s" % proto_num

        return proto_str

    def apply_port_filter(self, port):
        pass

    def _add_flow(self, **kwargs):
        LOG.debug("OFW add rule: %s", kwargs)
        ovs_fw.create_reg_numbers(kwargs)
        if self._deferred:
            self._int_br.add_flow(**kwargs)
        else:
            self._int_br.br.add_flow(**kwargs)

    def _del_flows(self, **kwargs):
        LOG.debug("OFW del rule: %s", kwargs)
        ovs_fw.create_reg_numbers(kwargs)
        if self._deferred:
            self._int_br.delete_flows(**kwargs)
        else:
            self._int_br.br.delete_flows(**kwargs)

    def install_fip_gateway_direct_flow(self, port, vif_port):
        if port["device_owner"] != "network:floatingip_agent_gateway":
            return

        if port['vinfo']["network_type"] in (
                constants.TYPE_VXLAN, constants.TYPE_GRE,
                constants.TYPE_GENEVE):
            # Some ports like router internal gateway will not install
            # the l2pop related flows, so we will transmit the ARP request
            # packet to tunnel bridge use NORMAL action as usual.
            port_name = cfg.CONF.OVS.int_peer_patch_port
            patch_ofport = self._int_br.br.get_port_ofport(port_name)
        elif port['vinfo']["network_type"] in [constants.TYPE_VLAN,
                                               constants.TYPE_FLAT]:
            physical_network = port['vinfo']["physical_network"]
            patch_ofport = ovs_fw.get_patch_ofport_for_physical_net(
                self._int_br.br, physical_network,
                port['vinfo'].get('segmentation_id'))
        else:
            return

        self._add_flow(
            priority=OF_T0_SELECT_TABLE_IN_PRIO,
            table=OF_TRANSIENT_TABLE,
            in_port=patch_ofport,
            dl_vlan=port['vinfo']['tag'],
            dl_dst=port['mac_address'],
            actions='resubmit(,%s)' % (
                OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE))

    def install_accepted_egress_direct_flow(self, mac, vlan_tag, dst_port,
                                            tunnel_direct_info=None,
                                            segment_id=None):
        if not cfg.CONF.AGENT.explicitly_egress_direct:
            return

        # Prevent flood for accepted egress traffic
        self._add_flow(
            table=OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE,
            priority=12,
            dl_dst=mac,
            reg_net=vlan_tag,
            actions='output:{:d}'.format(dst_port)
        )

        # For packets from patch ports.
        # Use more higher priority 13 to deal VLAN tag, because vlan id has
        # been modified in TRANSIENT table when port set disable_port_security
        self._add_flow(
            table=OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE,
            priority=13,
            dl_vlan=vlan_tag,
            dl_dst=mac,
            actions='strip_vlan,output:{:d}'.format(dst_port))

        # The former flow may not match, that means the destination port is
        # not in this host. So, we direct the packet to mapped bridge(s).
        if tunnel_direct_info:
            patch_ofport = ovs_lib.INVALID_OFPORT
            if tunnel_direct_info["network_type"] in (
                    constants.TYPE_VXLAN, constants.TYPE_GRE,
                    constants.TYPE_GENEVE):
                # Some ports like router internal gateway will not install
                # the l2pop related flows, so we will transmit the ARP request
                # packet to tunnel bridge use NORMAL action as usual.
                port_name = cfg.CONF.OVS.int_peer_patch_port
                patch_ofport = self._int_br.br.get_port_ofport(port_name)
            elif tunnel_direct_info["network_type"] == constants.TYPE_VLAN:
                physical_network = tunnel_direct_info["physical_network"]
                patch_ofport = ovs_fw.get_patch_ofport_for_physical_net(
                    self._int_br.br, physical_network, segment_id)

            if patch_ofport is not ovs_lib.INVALID_OFPORT:
                self._add_flow(
                    table=OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE,
                    priority=10,
                    dl_src=mac,
                    dl_dst="00:00:00:00:00:00/01:00:00:00:00:00",
                    reg_net=vlan_tag,
                    actions='mod_vlan_vid:{:d},output:{:d}'.format(
                        vlan_tag,
                        patch_ofport)
                )

                self._add_flow(
                    table=OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE,
                    priority=10,
                    dl_src=mac,
                    dl_dst="00:00:00:00:00:00/01:00:00:00:00:00",
                    dl_vlan=vlan_tag,
                    actions='output:{:d}'.format(
                        patch_ofport)
                )

    def delete_accepted_egress_direct_flow(self, mac, vlan_tag):
        self._del_flows(
            table=OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE,
            dl_dst=mac,
            reg_net=vlan_tag)

        self._del_flows(
            table=OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE,
            dl_vlan=vlan_tag,
            dl_dst=mac)

        self._del_flows(
            table=OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE,
            dl_src=mac,
            dl_dst="00:00:00:00:00:00/01:00:00:00:00:00",
            reg_net=vlan_tag
        )

        self._del_flows(
            table=OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE,
            dl_src=mac,
            dl_dst="00:00:00:00:00:00/01:00:00:00:00:00",
            dl_vlan=vlan_tag
        )

    def _initialize_egress_no_port_security(
            self, port, vif_port):
        self.unfiltered[port['device']] = (port, vif_port)
        self._add_flow(priority=OF_T0_ARP_NO_SG_PRIO,
            table=OF_ZERO_TABLE,
            in_port=vif_port.ofport,
            proto='arp',
            actions='resubmit(,%s)' % (
                OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE))
        # Install IPv6 ingress flows from provider bridge with VLAN ID
        if port['vinfo']["network_type"] == constants.TYPE_VLAN:
            for icmpv6_type in IPV6_ND_MESSAGES_NO_NS:
                self._add_flow(priority=OF_TO_ICMPV6_INT_PRIO,
                               table=OF_ZERO_TABLE,
                               proto=self._write_proto(
                                   IPv6, constants.PROTO_NAME_IPV6_ICMP),
                               icmpv6_type=icmpv6_type,
                               dl_vlan=port['vinfo']['segmentation_id'],
                               dl_dst=vif_port.vif_mac,
                               actions='strip_vlan,output:{:d}'.format(
                                   vif_port.ofport)
                               )
            for ip6_addr in self._get_port_ipv6_addresses(port, vif_port):
                self._add_flow(priority=OF_TO_ICMPV6_INT_PRIO,
                               table=OF_ZERO_TABLE,
                               proto=self._write_proto(
                                   IPv6, constants.PROTO_NAME_IPV6_ICMP),
                               icmpv6_type=ICMPv6_TYPE_NS,
                               dl_vlan=port['vinfo']['segmentation_id'],
                               nd_target=ip6_addr,
                               actions='strip_vlan,output:{:d}'.format(
                                   vif_port.ofport)
                               )
        if port["device_owner"] != constants.DEVICE_OWNER_DVR_INTERFACE:
            # dvr_agent will handle the DVR interface flows
            self._add_flow(
                table=OF_ZERO_TABLE,
                priority=OF_T0_NO_SG,
                in_port=vif_port.ofport,
                actions='resubmit(,%d)' % (DVR_PRE_QOS_TABLE)
            )
        # Not modify VLAN to allow IPv6 NS broadcast requests to propagate
        # normally
        self._add_flow(
            table=OF_TRANSIENT_TABLE,
            priority=100,
            in_port=vif_port.ofport,
            actions='set_field:%d->reg%d,'
                    'set_field:%d->reg%d,'
                    'resubmit(,%d)' % (
                        vif_port.ofport,
                        ovsfw_consts.REG_PORT,
                        port['vinfo']['tag'],
                        ovsfw_consts.REG_NET,
                        TRAFFIC_MIRROR_TABLE)
        )
        # Allows to access internal services by modifying VLAN to IP protocol,
        self._add_flow(
            table=OF_TRANSIENT_TABLE,
            priority=101,
            proto=self._write_proto(IPv4),
            in_port=vif_port.ofport,
            actions='mod_vlan_vid:%s,'
                    'set_field:%d->reg%d,'
                    'set_field:%d->reg%d,'
                    'resubmit(,%d)' % (
                        port['vinfo']['tag'],
                        vif_port.ofport,
                        ovsfw_consts.REG_PORT,
                        port['vinfo']['tag'],
                        ovsfw_consts.REG_NET,
                        TRAFFIC_MIRROR_TABLE)
        )
        # Install ingress flows from provider bridge
        if port['vinfo']["network_type"] == constants.TYPE_VLAN:
            action = self._process_resubmit_dscp_table(port)
            for fixed_ip in port['fixed_ips']:
                if self._ip_version_from_address(fixed_ip) == IPv4:
                    self._add_flow(
                        table=OF_TRANSIENT_TABLE,
                        priority=50,
                        proto=self._write_proto(IPv4),
                        dl_vlan=port['vinfo']['tag'],
                        nw_dst=fixed_ip,
                        dl_dst=vif_port.vif_mac,
                        actions='resubmit(,%s)' % TRAFFIC_MIRROR_TABLE
                    )
                    self._add_flow(
                        table=OF_SEC_EXT_TABLE,
                        priority=50,
                        proto=self._write_proto(IPv4),
                        dl_vlan=port['vinfo']['tag'],
                        nw_dst=fixed_ip,
                        dl_dst=vif_port.vif_mac,
                        actions=(action + 'resubmit(,%s)' %
                                 OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE)
                    )
                else:
                    self._add_flow(
                        table=OF_TRANSIENT_TABLE,
                        priority=50,
                        proto=self._write_proto(IPv6),
                        dl_vlan=port['vinfo']['tag'],
                        ipv6_dst=fixed_ip,
                        dl_dst=vif_port.vif_mac,
                        actions='resubmit(,%s)' % TRAFFIC_MIRROR_TABLE
                    )
                    self._add_flow(
                        table=OF_SEC_EXT_TABLE,
                        priority=50,
                        proto=self._write_proto(IPv6),
                        dl_vlan=port['vinfo']['tag'],
                        ipv6_dst=fixed_ip,
                        dl_dst=vif_port.vif_mac,
                        actions=(action + 'resubmit(,%s)' %
                                 OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE)
                    )
        # table=71
        self._add_flow(
            table=OF_SELECT_TABLE,
            priority=80,
            reg_port=vif_port.ofport,
            reg_net=port['vinfo']['tag'],
            actions='resubmit(,%d)' % (OF_SEC_EXT_TABLE)
        )
        self._add_flow(
            table=OF_SELECT_TABLE,
            priority=60,
            dl_vlan=port['vinfo']['tag'],
            dl_dst=vif_port.vif_mac,
            actions='resubmit(,%d)' % (OF_SEC_EXT_TABLE)
        )
        # table=82
        self._add_flow(
            table=OF_SEC_EXT_TABLE,
            priority=80,
            reg_port=vif_port.ofport,
            actions='resubmit(,%d)' % (
                OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE)
        )

        self.install_accepted_egress_direct_flow(
            vif_port.vif_mac, port['vinfo']['tag'], vif_port.ofport,
            tunnel_direct_info=port['vinfo'],
            segment_id=port['vinfo'].get("segmentation_id"))

        self.install_fip_gateway_direct_flow(port, vif_port)

    def _add_base_flows(self, port, vif_port):
        """Set base flows for every port."""
        self._add_zero_table(port, vif_port)
        self._add_selection_table(port, vif_port)
        self._add_selection_table_services(port, vif_port)
        self._add_selection_table_port_block(port, vif_port)
        self._add_egress_antispoof(port, vif_port)
        self._add_egress_services(port, vif_port)
        self._add_ingress_allow_outbound_traffic(port)
        self._add_ingress_services(port, vif_port)

        self.install_accepted_egress_direct_flow(
            vif_port.vif_mac, port['vinfo']['tag'], vif_port.ofport,
            tunnel_direct_info=port['vinfo'],
            segment_id=port['vinfo'].get("segmentation_id"))

        # Process allowed address mac egress direct flows
        for allowed_pair in port.get('allowed_address_pairs', []):
            mac = allowed_pair.get('mac_address')
            if not mac:
                continue
            self.install_accepted_egress_direct_flow(
                mac, port['vinfo']['tag'], vif_port.ofport,
                tunnel_direct_info=port['vinfo'],
                segment_id=port['vinfo'].get("segmentation_id"))

    def _get_port_ipv6_addresses(self, port, vif=None):
        addresses = set(port['fixed_ips'])
        if vif:
            mac_addresses = {vif.vif_mac}
        elif port.get('mac_address'):
            mac_addresses = {port.get('mac_address')}
        else:
            mac_addresses = set()
        if port.get('allowed_address_pairs'):
            addresses |= {p['ip_address']
                          for p in port['allowed_address_pairs']}
            mac_addresses |= {p['mac_address']
                              for p in port['allowed_address_pairs']
                              if p.get('mac_address')}

        ipv6_addresses = {ip for ip in addresses
                          if netaddr.IPNetwork(ip).version == 6}
        # Allow neighbor advertisements for LLA address.
        ipv6_addresses |= {str(netutils.get_ipv6_addr_by_EUI64(
                               constants.IPv6_LLA_PREFIX, mac))
                           for mac in mac_addresses}

        return ipv6_addresses

    def _get_port_ipv6_pair_addresses(self, port):
        # TODO(shaojb): process ipv6 allowed address pairs
        ip6_mac_pairs = []
        for ip_addr in port.get('allowed_address_pairs', []):
            if netaddr.IPNetwork(ip_addr['ip_address']).version == 6:
                ip6_mac_pairs.append(
                    (ip_addr['ip_address'], ip_addr['mac_address']))
        return ip6_mac_pairs

    def _add_zero_table(self, port, vif_port):
        """Set arp flows. The rest of the traffic is sent to
            SELECT_TABLE.
            """

        # ARP and ND traffic to be delivered to an internal port.
        for fixed_ip in port['fixed_ips']:
            # IPv4, ARP.
            if self._ip_version_from_address(fixed_ip) == IPv4:
                self._add_flow(priority=OF_T0_ARP_INT_PRIO,
                    table=OF_ZERO_TABLE,
                    proto=self._write_proto(IPv4, "arp"),
                    dl_vlan=port['vinfo']['tag'],
                    nw_dst=fixed_ip,
                    actions='strip_vlan,output:%s' % vif_port.ofport)

        for ip6_addr in self._get_port_ipv6_addresses(port, vif_port):
            # IPv6, NS and NA.
            for icmpv6_type in IPV6_ND_MESSAGES:
                self._add_flow(priority=OF_T0_ARP_INT_PRIO,
                    table=OF_TRANSIENT_TABLE,
                    proto=self._write_proto(IPv6,
                            constants.PROTO_NAME_IPV6_ICMP),
                    # dl_vlan=segmentation_id,
                    icmpv6_type=icmpv6_type,
                    ipv6_dst=ip6_addr,
                    actions='output:%s' % vif_port.ofport)

        if port['vinfo']["network_type"] == constants.TYPE_VLAN:
            segmentation_id = port['vinfo']['segmentation_id']
            # Process ipv6 allowed address NA
            for ip6_addr in self._get_port_ipv6_pair_addresses(port):
                if not ip6_addr:
                    continue
                self._add_flow(priority=OF_T0_ARP_INT_PRIO,
                               table=OF_ZERO_TABLE,
                               proto=self._write_proto(
                                   IPv6, constants.PROTO_NAME_IPV6_ICMP),
                               dl_vlan=segmentation_id,
                               dl_dst=ip6_addr[1],
                               icmpv6_type=ICMPV6_TYPE_NA,
                               actions='strip_vlan,output:%s' %
                                       vif_port.ofport)
            for ip6_addr in self._get_port_ipv6_addresses(port, vif_port):
                # IPv6, NS and NA.
                self._add_flow(priority=OF_T0_ARP_INT_PRIO,
                    table=OF_ZERO_TABLE,
                    proto=self._write_proto(IPv6,
                        constants.PROTO_NAME_IPV6_ICMP),
                    dl_vlan=segmentation_id,
                    icmpv6_type=ICMPv6_TYPE_NS,
                    nd_target=ip6_addr,
                    actions='strip_vlan,output:%s' % vif_port.ofport)
            self._add_flow(priority=OF_T0_ARP_INT_PRIO,
                    table=OF_ZERO_TABLE,
                    proto=self._write_proto(IPv6,
                        constants.PROTO_NAME_IPV6_ICMP),
                    dl_vlan=segmentation_id,
                    dl_dst=port['mac_address'],
                    icmpv6_type=ICMPV6_TYPE_NA,
                    actions='strip_vlan,output:%s' % vif_port.ofport)
            self._add_flow(priority=OF_T0_ARP_INT_PRIO,
                    table=OF_ZERO_TABLE,
                    proto=self._write_proto(IPv6,
                        constants.PROTO_NAME_IPV6_ICMP),
                    dl_vlan=segmentation_id,
                    icmpv6_type=ICMPV6_TYPE_RA,
                    actions='mod_vlan_vid:%s,resubmit(,%s)' % (
                        port['vinfo']['tag'],
                        OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE))

        # Internal port ARP messages to be delivered out of br-int.
        self._add_flow(priority=OF_T0_ARP_EXT_PRIO,
            table=OF_ZERO_TABLE,
            in_port=vif_port.ofport,
            proto='arp',
            actions='resubmit(,%s)' % ovs_constants.ARP_SPOOF_TABLE)

        self._add_flow(priority=OF_T0_ARP_EXT_PRIO,
            table=OF_TRANSIENT_TABLE,
            proto='arp',
            actions='resubmit(,%s)' % (
                TRAFFIC_MIRROR_TABLE))
        self._add_flow(priority=OF_TRAFFIC_ARP_FORBIDDEN_PRI,
                       table=TRAFFIC_MIRROR_TABLE,
                       proto='arp',
                       actions='resubmit(,%s)' %
                               OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE
                       )

        # Internal port NS and NA messages to be delivered out of br-int.
        for icmpv6_type in IPV6_ND_MESSAGES:
            self._add_flow(priority=OF_T0_ARP_EXT_PRIO,
                table=OF_ZERO_TABLE,
                proto=self._write_proto(IPv6,
                                        constants.PROTO_NAME_IPV6_ICMP),
                icmpv6_type=icmpv6_type,
                actions='resubmit(,%s)' % (
                    OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE))

        # Select traffic.
        # Incoming internal traffic: check mac, mod vlan.
        # Process allowed address pairs that could send and reply by using
        # mac address.
        for addr_pair in port.get("allowed_address_pairs", []):
            mac = addr_pair.get("mac_address")
            if not mac:
                continue
            self._add_flow(priority=OF_T0_SELECT_TABLE_IN_PRIO,
                           table=OF_TRANSIENT_TABLE,
                           in_port=vif_port.ofport,
                           dl_src=mac,
                           actions="mod_vlan_vid:%s,"
                                   "load:0->NXM_NX_REG0[0..11],"
                                   "load:0->NXM_NX_REG1[0..11],"
                                   "resubmit(,%s)"
                                   % (port['vinfo']['tag'],
                                      TRAFFIC_MIRROR_TABLE))

        self._add_flow(priority=OF_T0_SELECT_TABLE_IN_PRIO,
                       table=OF_TRANSIENT_TABLE,
                       in_port=vif_port.ofport,
                       dl_src=port['mac_address'],
                       actions="mod_vlan_vid:%s,"
                               "load:0->NXM_NX_REG0[0..11],"
                               "load:0->NXM_NX_REG1[0..11],"
                               "resubmit(,%s)"
                               % (port['vinfo']['tag'], TRAFFIC_MIRROR_TABLE))

        # Incoming external traffic: check external vlan tag, mod vlan.
        self._add_flow(priority=OF_T0_SELECT_TABLE_EXT_PRIO,
                       table=OF_TRANSIENT_TABLE,
                       dl_vlan=port['vinfo']['tag'],
                       actions="load:%s->NXM_NX_REG0[0..11],"
                               "load:0->NXM_NX_REG1[0..11],"
                               "resubmit(,%s)"
                               % (port['vinfo']['tag'],
                                  TRAFFIC_MIRROR_TABLE))

        # Block rest of traffic.
        self._add_flow(priority=OF_T0_BLOCK,
                       table=OF_TRANSIENT_TABLE,
                       actions="drop")

    def _add_selection_table(self, port, vif_port):
        """Set traffic selection basic rules.
            Allows all internal traffic matching mac/ip to egress table.
            Allows all extenal traffic matching dst mac to ingress table.
        """
        for fixed_ip in port['fixed_ips']:
            if self._ip_version_from_address(fixed_ip) == IPv4:
                self._add_flow(priority=OF_SEL_EGRESS_ALLOW_PRIO,
                    table=OF_SELECT_TABLE,
                    in_port=vif_port.ofport,
                    proto=self._write_proto(IPv4),
                    dl_vlan=port['vinfo']['tag'],
                    dl_src=port['mac_address'],
                    nw_src=fixed_ip,
                    actions='resubmit(,%s)' %
                        (OF_EGRESS_TABLE))

            if self._ip_version_from_address(fixed_ip) == IPv6:
                self._add_flow(priority=OF_SEL_EGRESS_ALLOW_PRIO,
                    table=OF_SELECT_TABLE,
                    in_port=vif_port.ofport,
                    proto=self._write_proto(IPv6),
                    dl_vlan=port['vinfo']['tag'],
                    dl_src=port['mac_address'],
                    ipv6_src=fixed_ip,
                    actions='resubmit(,%s)' %
                        (OF_EGRESS_TABLE))

        for ip6_addr in self._get_port_ipv6_pair_addresses(port):
            if not ip6_addr:
                continue
            self._add_flow(priority=OF_SEL_EGRESS_ALLOW_PRIO,
                           table=OF_SELECT_TABLE,
                           in_port=vif_port.ofport,
                           proto=self._write_proto(IPv6),
                           dl_vlan=port['vinfo']['tag'],
                           dl_src=ip6_addr[1],
                           ipv6_src=ip6_addr[0],
                           actions='resubmit(,%s)' %
                                   (OF_EGRESS_TABLE))
        # External traffic to ingress processing table
        self._add_flow(
            priority=OF_SEL_INGRESS_PRIO,
            table=OF_SELECT_TABLE,
            dl_vlan=port['vinfo']['tag'],
            dl_dst=port['mac_address'],
            actions='resubmit(,%d)'
                    % (OF_NO_SG_VLAN_MATCH))
        self._add_flow(
            priority=OF_SEL_INGRESS_PRIO,
            table=OF_SELECT_TABLE,
            reg_net=port['vinfo']['tag'],
            dl_dst=port['mac_address'],
            actions='resubmit(,%d)'
                    % (OF_NO_SG_VLAN_MATCH))

        # Process allowed address pairs that could receive packets by using
        # mac address.
        for allowed_pair in port.get('allowed_address_pairs', []):
            mac_addr = allowed_pair.get('mac_address')
            if not mac_addr:
                continue
            self._add_flow(
                priority=OF_SEL_INGRESS_PRIO,
                table=OF_SELECT_TABLE,
                dl_vlan=port['vinfo']['tag'],
                dl_dst=mac_addr,
                actions='resubmit(,%d)'
                        % (OF_NO_SG_VLAN_MATCH))
            self._add_flow(
                priority=OF_SEL_INGRESS_PRIO,
                table=OF_SELECT_TABLE,
                reg_net=port['vinfo']['tag'],
                dl_dst=mac_addr,
                actions='resubmit(,%d)'
                        % (OF_NO_SG_VLAN_MATCH))

        # table=73
        self._add_flow(
            table=OF_NO_SG_VLAN_MATCH,
            priority=80,
            reg_net=port['vinfo']['tag'],
            actions=('set_field:0->reg%d,'
                     'set_field:0->reg%d,'
                     'mod_vlan_vid:%s,'
                     'resubmit(,%d)') % (
                        ovsfw_consts.REG_PORT,
                        ovsfw_consts.REG_NET,
                        port['vinfo']['tag'],
                        OF_ACCEPT_OR_INGRESS_TABLE)
        )

    def _add_selection_table_services(self, port, vif_port):
        """Selection table services:
           Allows DHCP traffic to request an IP address
           IGMP snooping/MLD traffic and multicast traffic.
        """
        # Allow DHCPv4 requests from invalid address
        self._add_flow(priority=OF_SEL_EGRESS_ALLOW_PRIO,
                       table=OF_SELECT_TABLE,
                       in_port=vif_port.ofport,
                       proto=self._write_proto(IPv4,
                                               constants.PROTO_NAME_UDP),
                       dl_vlan=port['vinfo']['tag'],
                       dl_src=port['mac_address'],
                       tp_src=68,
                       tp_dst=67,
                       actions="resubmit(,%s)" % (OF_EGRESS_TABLE))
        self._add_flow(priority=OF_SEC_EXT_DHCP_PRIO,
                       table=OF_SEC_EXT_TABLE,
                       in_port=vif_port.ofport,
                       proto=self._write_proto(IPv4,
                                               constants.PROTO_NAME_UDP),
                       dl_vlan=port['vinfo']['tag'],
                       dl_src=port['mac_address'],
                       tp_src=68,
                       tp_dst=67,
                       actions="strip_vlan,resubmit(,%s)" % (
                           OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE))

        # Allow DHCPv6
        self._add_flow(priority=OF_SEL_EGRESS_ALLOW_PRIO,
                       table=OF_SELECT_TABLE,
                       in_port=vif_port.ofport,
                       proto=self._write_proto(IPv6,
                                               constants.PROTO_NAME_UDP),
                       dl_vlan=port['vinfo']['tag'],
                       dl_src=port['mac_address'],
                       ipv6_dst="ff02::1:2",
                       tp_src=546,
                       tp_dst=547,
                       actions="resubmit(,%s)" % (OF_EGRESS_TABLE))
        self._add_flow(priority=OF_SEC_EXT_DHCP_PRIO,
                       table=OF_SEC_EXT_TABLE,
                       in_port=vif_port.ofport,
                       proto=self._write_proto(IPv6,
                                               constants.PROTO_NAME_UDP),
                       dl_vlan=port['vinfo']['tag'],
                       dl_src=port['mac_address'],
                       ipv6_dst="ff02::1:2",
                       tp_src=546,
                       tp_dst=547,
                       actions="strip_vlan,resubmit(,%s)" % (
                           OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE))

        # Allowed address pair
        for allowed_addr in port['allowed_address_pairs']:
            addr = netaddr.IPNetwork(allowed_addr['ip_address'])
            if addr.version == 4:
                self._add_flow(priority=OF_SEL_EGRESS_ALLOW_PRIO,
                               table=OF_SELECT_TABLE,
                               in_port=vif_port.ofport,
                               proto=self._write_proto(IPv4),
                               dl_vlan=port['vinfo']['tag'],
                               dl_src=allowed_addr['mac_address'],
                               nw_src=allowed_addr['ip_address'],
                               actions="resubmit(,%s)"
                                       % (OF_EGRESS_TABLE))
            else:
                self._add_flow(priority=OF_SEL_EGRESS_ALLOW_PRIO,
                               table=OF_SELECT_TABLE,
                               in_port=vif_port.ofport,
                               proto=self._write_proto(IPv6),
                               dl_vlan=port['vinfo']['tag'],
                               dl_src=allowed_addr['mac_address'],
                               ipv6_src=allowed_addr['ip_address'],
                               actions="resubmit(,%s)"
                                       % (OF_EGRESS_TABLE))

        if self._enable_multicast:
            for fixed_ip in port['fixed_ips']:
                if self._ip_version_from_address(fixed_ip) == IPv4:
                    # Add internal IGMP snooping traffic support. This traffic
                    # is sent to the bridge using the 'normal' action.
                    self._add_flow(priority=OF_SEL_SERVICES_INT_IGMP_PRIO,
                        table=OF_SELECT_TABLE,
                        in_port=vif_port.ofport,
                        proto=self._write_proto(IPv4, "igmp"),
                        dl_vlan=port['vinfo']['tag'],
                        dl_src=port['mac_address'],
                        dl_dst=MULTICAST_MAC,
                        nw_src=fixed_ip,
                        nw_dst=IPV4_MULTICAST,
                        actions='strip_vlan,normal')

                if self._ip_version_from_address(fixed_ip) == IPv6:
                    # Add internal MLD snooping traffic support.
                    self._add_flow(priority=OF_SEL_SERVICES_INT_IGMP_PRIO,
                        table=OF_SELECT_TABLE,
                        in_port=vif_port.ofport,
                        proto=self._write_proto(IPv6,
                            constants.PROTO_NAME_IPV6_ICMP),
                        dl_vlan=port['vinfo']['tag'],
                        dl_src=port['mac_address'],
                        dl_dst=MULTICAST_MAC,
                        ipv6_src=fixed_ip,
                        ipv6_dst=IPV6_MULTICAST,
                        actions='strip_vlan,normal')

            # Add external IGMP snooping traffic support.
            self._add_flow(priority=OF_SEL_SERVICES_EXT_IGMP_PRIO,
                table=OF_SELECT_TABLE,
                proto=self._write_proto(IPv4, "igmp"),
                dl_vlan=port['vinfo']['tag'],
                dl_dst=MULTICAST_MAC,
                nw_dst=IPV4_MULTICAST,
                actions='normal')

            # Add external MLD snooping traffic support.
            self._add_flow(priority=OF_SEL_SERVICES_EXT_IGMP_PRIO,
                table=OF_SELECT_TABLE,
                proto=self._write_proto(IPv6,
                                        constants.PROTO_NAME_IPV6_ICMP),
                dl_vlan=port['vinfo']['tag'],
                dl_dst=MULTICAST_MAC,
                ipv6_dst=IPV6_MULTICAST,
                actions='normal')

            # Allow external multicast traffic to skip the internal MAC filter.
            # This traffic is sent to the ingress table. Only TCP and UDP.
            # REG0 in external traffic must match the internal VLAN tag.
            for proto in [constants.PROTO_NAME_TCP,
                          constants.PROTO_NAME_UDP]:
                self._add_flow(priority=OF_SEL_SERVICES_EXT_MULTICAST_PRIO,
                    table=OF_SELECT_TABLE,
                    reg0="%s" % port['vinfo']['tag'],
                    proto=self._write_proto(IPv4, proto),
                    dl_vlan=port['vinfo']['tag'],
                    dl_dst=MULTICAST_MAC,
                    nw_dst=IPV4_MULTICAST,
                    actions='load:1->NXM_NX_REG1[0..11],'
                            'resubmit(,%s)' % OF_ACCEPT_OR_INGRESS_TABLE)
                self._add_flow(priority=OF_SEL_SERVICES_EXT_MULTICAST_PRIO,
                    table=OF_SELECT_TABLE,
                    reg0="%s" % port['vinfo']['tag'],
                    proto=self._write_proto(IPv6, proto),
                    dl_vlan=port['vinfo']['tag'],
                    dl_dst=MULTICAST_MAC,
                    ipv6_dst=IPV6_MULTICAST,
                    actions='load:1->NXM_NX_REG1[0..11],'
                            'resubmit(,%s)' % OF_ACCEPT_OR_INGRESS_TABLE)

    def _add_selection_table_port_block(self, port, vif_port=None):
        """Block rest of the traffic.
            Drop all traffic not generated by or to a VM.
            """
        for eth_type in ETH_PROTOCOL_TABLE.keys():
            self._add_flow(
                priority=OF_DROP_TRAFFIC_PRIO,
                table=OF_SELECT_TABLE,
                proto=self._write_proto(eth_type),
                dl_vlan=port['vinfo']['tag'],
                actions='drop')
        # Prevent to send packets with non allocated or non allowed ip
        # address to prevent ip spoof attacks.
        if (port.get('port_security_enabled') and
                cfg.CONF.SECURITYGROUP.anti_ip_spoof and vif_port):
            self._add_flow(priority=OF_SEL_EGRESS_DROP_PRIO,
                           table=OF_SELECT_TABLE,
                           in_port=vif_port.ofport,
                           proto=self._write_proto(IPv4),
                           dl_vlan=port['vinfo']['tag'],
                           actions='drop')
            self._add_flow(priority=OF_SEL_EGRESS_DROP_PRIO,
                           table=OF_SELECT_TABLE,
                           in_port=vif_port.ofport,
                           proto=self._write_proto(IPv6),
                           dl_vlan=port['vinfo']['tag'],
                           actions='drop')

    def _add_egress_antispoof(self, port, vif_port):
        """Set antispoof rules.
            Antispoof rules take precedence to any rules set by
            the tenant in the security group.
            """
        # No DHCPv4 server out from port.
        self._add_flow(priority=OF_EGRESS_ANTISPOOF_PRIO,
                       table=OF_EGRESS_TABLE,
                       in_port=vif_port.ofport,
                       proto=self._write_proto(IPv4,
                                               constants.PROTO_NAME_UDP),
                       dl_vlan=port['vinfo']['tag'],
                       udp_src=67,
                       udp_dst=68,
                       actions='drop')

        # No DHCPv6 server out from port.
        self._add_flow(priority=OF_EGRESS_ANTISPOOF_PRIO,
                       table=OF_EGRESS_TABLE,
                       in_port=vif_port.ofport,
                       proto=self._write_proto(IPv6,
                                               constants.PROTO_NAME_UDP),
                       dl_vlan=port['vinfo']['tag'],
                       udp_src=547,
                       udp_dst=546,
                       actions='drop')

    def _add_egress_services(self, port, vif_port):
        """Add service rules.
            Allows traffic to DHCPv4/v6 servers.
            Allows icmp traffic.
            """
        # DHCP & DHCPv6.
        for eth_type, udp_src, udp_dst in [(IPv4, 68, 67), (IPv6, 546, 547)]:
            self._add_flow(
                table=OF_EGRESS_TABLE,
                priority=OF_EGRESS_SERVICES_PRIO,
                dl_vlan=port['vinfo']['tag'],
                dl_src=port['mac_address'],
                in_port=vif_port.ofport,
                proto=self._write_proto(eth_type, constants.PROTO_NAME_UDP),
                udp_src=udp_src,
                udp_dst=udp_dst,
                actions='resubmit(,%s)' % OF_ACCEPT_OR_INGRESS_TABLE)

        # Allows ICMP router advertisement / router selection.
        for type_ in IPV4_ROUTER_MESSAGES:
            self._add_flow(
                table=OF_EGRESS_TABLE,
                priority=OF_EGRESS_SERVICES_PRIO,
                dl_vlan=port['vinfo']['tag'],
                dl_src=port['mac_address'],
                proto=self._write_proto(IPv4,
                                        constants.PROTO_NAME_ICMP),
                icmp_type=type_,
                actions='resubmit(,%s)' % OF_ACCEPT_OR_INGRESS_TABLE)

        # Allows IPv6 MLD messages.
        for icmpv6_type in IPV6_MLD_MESSAGES:
            self._add_flow(
                table=OF_EGRESS_TABLE,
                priority=OF_EGRESS_SERVICES_PRIO,
                dl_vlan=port['vinfo']['tag'],
                dl_src=port['mac_address'],
                in_port=vif_port.ofport,
                proto=self._write_proto(IPv6,
                                        constants.PROTO_NAME_IPV6_ICMP),
                icmpv6_type=icmpv6_type,
                actions='resubmit(,%s)' % OF_ACCEPT_OR_INGRESS_TABLE)

    def _add_ingress_allow_outbound_traffic(self, port):
        """Allows ingress outbound traffic.
            By default, all ingress traffic not matching any internal port
            in the bridge is sent to the external ports.
            """
        # If the traffic do not match any mac address inside the bridge,
        # send the traffic to the INGRESS_EXT table.
        self._add_flow(
            table=OF_ACCEPT_OR_INGRESS_TABLE,
            priority=OF_INGRESS_OUTBOUND_PRIO,
            dl_vlan=port['vinfo']['tag'],
            actions='resubmit(,%s)' % OF_SEC_EXT_TABLE)

        # Blocks all traffic in this table if it's for an internal
        # port (mac address).
        self._add_flow(
            table=OF_SEC_EXT_TABLE,
            priority=OF_SEC_EXT_BLOCK_INT_MAC_PRIO,
            dl_vlan=port['vinfo']['tag'],
            dl_dst=port['mac_address'],
            actions='drop')

        # Block all traffic in allowed address pairs that control all flow
        # must transport security group rules.
        for allowed_addr in port.get('allowed_address_pairs', []):
            mac = allowed_addr.get('mac_address')
            if not mac:
                continue
            self._add_flow(
                table=OF_SEC_EXT_TABLE,
                priority=OF_SEC_EXT_BLOCK_INT_MAC_PRIO,
                dl_vlan=port['vinfo']['tag'],
                dl_dst=allowed_addr['mac_address'],
                actions='drop')

        # TODO(liuyulong): revisit this
        # Blocks all traffic in this table if was sent by an external
        # source. Prevents from sending back the traffic.
        # self._add_flow(
        #     table=OF_SEC_EXT_TABLE,
        #     priority=OF_SEC_EXT_BLOCK_EXT_SOURCE_PRIO,
        #     dl_vlan=port['vinfo']['tag'],
        #     reg0="%s" % port['vinfo']['tag'],
        #     actions='drop')

        # Goto table 94 to go outside world or to local port
        action = self._process_resubmit_dscp_table(port)
        self._add_flow(
            table=OF_SEC_EXT_TABLE,
            priority=OF_SEC_EXT_ALLOW_EXT_TRAFFIC_PRIO,
            dl_vlan=port['vinfo']['tag'],
            actions=(action + 'resubmit(,%s)' %
                     OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE))

    def _process_resubmit_dscp_table(self, port):
        action = ''
        if cfg.CONF.AGENT.enable_lbaas_dscp:
            port_attrs = port.get('cloud_attributes')
            if port_attrs and "dscp_learn" in port_attrs['cloud_attrs']:
                action = ',resubmit(,%s)' % ovs_constants.DSCP_TABLE
        return action

    def _add_ingress_services(self, port, vif_port):
        """Add service rules.dl_vlan=port['vinfo']['tag'],
            Allows traffic to DHCPv4/v6 servers
            Allows specific icmp traffic (RA messages).
            """
        # DHCP & DHCPv6.
        for eth_type, udp_src, udp_dst in [(IPv4, 67, 68), (IPv6, 547, 546)]:
            self._add_flow(
                table=OF_ACCEPT_OR_INGRESS_TABLE,
                priority=OF_INGRESS_SERVICES_PRIO,
                proto=self._write_proto(eth_type, constants.PROTO_NAME_UDP),
                dl_vlan=port['vinfo']['tag'],
                dl_dst=port['mac_address'],
                udp_src=udp_src,
                udp_dst=udp_dst,
                actions=self._get_ingress_actions(vif_port))

        # ICMP RA messages.
        for type_ in IPV4_ROUTER_MESSAGES:
            self._add_flow(
                table=OF_ACCEPT_OR_INGRESS_TABLE,
                priority=OF_INGRESS_SERVICES_PRIO,
                proto=self._write_proto(IPv4,
                                        constants.PROTO_NAME_ICMP),
                dl_vlan=port['vinfo']['tag'],
                dl_dst=port['mac_address'],
                icmp_type=type_,
                actions=self._get_ingress_actions(vif_port))

        # ICMP6 MLD messages.
        for icmpv6_type in IPV6_MLD_MESSAGES:
            self._add_flow(
                table=OF_ACCEPT_OR_INGRESS_TABLE,
                priority=OF_INGRESS_SERVICES_PRIO,
                proto=self._write_proto(IPv6,
                                        constants.PROTO_NAME_IPV6_ICMP),
                dl_vlan=port['vinfo']['tag'],
                dl_dst=port['mac_address'],
                icmpv6_type=icmpv6_type,
                actions=self._get_ingress_actions(vif_port))

        if self._enable_multicast:
            # Block the multicast traffic in the external ingress table.
            self._add_flow(
                table=OF_SEC_EXT_TABLE,
                priority=OF_SEC_EXT_BLOCK_MULTICAST_PRIO,
                dl_vlan=port['vinfo']['tag'],
                reg1="1",
                actions='drop')

    def _get_ingress_actions(self, vif_port):
        return 'strip_vlan,output:%(oport)s' % \
               {'oport': vif_port.ofport}

    def _remove_flows(self, port, vif_port=None):
        # Remove antispoof rules.
        if self._filtered_in_ports.get(port['device']):
            ofport = self._filtered_in_ports.get(port['device'])
            self._del_flows(in_port=ofport)
            self._del_flows(
                table=OF_SELECT_TABLE,
                reg_port=ofport,
                reg_net=port['vinfo']['tag'])
        self._del_flows(
            table=OF_SELECT_TABLE,
            dl_vlan=port['vinfo']['tag'],
            dl_dst=port["mac_address"])

        # Remove ARP rules.
        for ipv4 in [ip for ip in port['fixed_ips'] if
                     self._ip_version_from_address(ip) == IPv4]:
            self._del_flows(table=OF_ZERO_TABLE, nw_dst=ipv4,
                            proto=self._write_proto(IPv4, "arp"))
        # Remove ND rules.
        for ipv6 in [ip for ip in port['fixed_ips'] if
                     self._ip_version_from_address(ip) == IPv6]:
            self._del_flows(table=OF_ZERO_TABLE, ipv6_dst=ipv6,
                proto=self._write_proto(IPv6, constants.PROTO_NAME_IPV6_ICMP))

        # Remove manual and "learn action" rules.
        for proto in [constants.PROTO_NAME_TCP,
                      constants.PROTO_NAME_UDP,
                      constants.PROTO_NAME_ICMP]:
            for ether in ETH_PROTOCOL_TABLE.keys():
                self._del_flows(dl_vlan=port['vinfo']['tag'],
                                proto=self._write_proto(ether, proto),
                                dl_src=port["mac_address"])
                self._del_flows(dl_vlan=port['vinfo']['tag'],
                                proto=self._write_proto(ether, proto),
                                dl_dst=port["mac_address"])
        self._del_flows(dl_vlan=port['vinfo']['tag'],
                        dl_src=port["mac_address"])
        self._del_flows(dl_vlan=port['vinfo']['tag'],
                        dl_dst=port["mac_address"])
        self.delete_accepted_egress_direct_flow(
            port["mac_address"], port['vinfo']['tag'])
        self._delete_direct_flows_no_port_security(port, vif_port)

        # Process allowed address pair that remove all mac flows
        for address_pair in port.get('allowed_address_pairs', []):
            self._del_flows(reg_net=port['vinfo']['tag'],
                            dl_dst=address_pair['mac_address'])
            self._del_flows(dl_vlan=port['vinfo']['tag'],
                            dl_dst=address_pair['mac_address'])
            self._del_flows(reg_net=port['vinfo']['tag'],
                            dl_src=address_pair['mac_address'])
            # Remove existed security group rules' flows
            self._del_flows(dl_vlan=port['vinfo']['tag'],
                            dl_src=address_pair['mac_address'])

        # Remove ipv6 EUI LLA NS\NA\RS\RA etc flows
        # and ipv6 allowed address NS\NA\RS\RA etc flows
        for ip6_addr in self._get_port_ipv6_addresses(port):
            if not ip6_addr:
                continue
            for icmpv6_type in IPV6_ND_MESSAGES:
                self._del_flows(table=OF_TRANSIENT_TABLE,
                                proto=self._write_proto(
                                    IPv6, constants.PROTO_NAME_IPV6_ICMP),
                                icmpv6_type=icmpv6_type,
                                ipv6_dst=ip6_addr)

    def _write_multicast_flow(self, flow, direction, port, port_match,
                              priority, ip_version, protocol):
        """Write a flow for the manual rule, allowing multicast traffic.
            """
        # Multicast ingress traffic.
        # Check the traffic protocol: only tcp or udp. Also check the
        # multicast destination MAC and IP.
        if self._enable_multicast \
                and direction == INGRESS_DIRECTION \
                and protocol in [constants.PROTO_NAME_TCP,
                                 constants.PROTO_NAME_UDP]:
            hp_flow = dict.copy(flow)
            ip_dst = IPV6_MULTICAST if ip_version == IPv6 else IPV4_MULTICAST
            hp_flow[OF_MNEMONICS[ip_version]['ip_dst']] = ip_dst
            hp_flow['dl_vlan'] = port['vinfo']['tag']
            hp_flow['priority'] = priority
            hp_flow['dl_dst'] = MULTICAST_MAC
            hp_flow['table'] = OF_ACCEPT_OR_INGRESS_TABLE
            hp_flow['actions'] = \
                "move:NXM_NX_REG0[0..11]->NXM_OF_VLAN_TCI[0..11],normal" if \
                flow.get("actions") != ovsfw_consts.ACTION_DENY else "drop"
            self._write_flows_per_port_match(hp_flow, port_match)

    def _get_learn_action_rule(self, direction, priority,
                               port_range_min, port_range_max,
                               eth_type, ip_proto, vif_port, port=None):
        # Write protocol string.
        proto_str = self._write_proto(eth_type, ip_proto)
        port_dst_str = ""
        port_src_str = ""
        if ip_proto in [constants.PROTO_NAME_TCP,
                        constants.PROTO_NAME_UDP]:
            # Known L4 protocols with configurable ports.
            port_dst_str = \
                "NXM_OF_%(ip_proto)s_DST[]=NXM_OF_%(ip_proto)s_SRC[]," \
                % {'ip_proto': ip_proto.upper()}
            port_src_str = \
                "NXM_OF_%(ip_proto)s_SRC[]=NXM_OF_%(ip_proto)s_DST[]," \
                % {'ip_proto': ip_proto.upper()}
        elif ip_proto == constants.PROTO_NAME_SCTP:
            # L4 SCTP protocols use sctp_src, sctp_dst, OXM_OF_SCTP_SRC,
            # OXM_OF_SCTP_DST
            port_dst_str = \
                "OXM_OF_%(ip_proto)s_DST[]=OXM_OF_%(ip_proto)s_SRC[]," \
                % {'ip_proto': ip_proto.upper()}
            port_src_str = \
                "OXM_OF_%(ip_proto)s_SRC[]=OXM_OF_%(ip_proto)s_DST[]," \
                % {'ip_proto': ip_proto.upper()}

        # Setup ICMPv4/v6 type and code.
        icmp_type = ""
        icmp_code = ""
        if ip_proto == constants.PROTO_NAME_ICMP:
            if port_range_min == 8:
                icmp_type = "icmp_type=%s," % 0
            elif port_range_min == 13:
                icmp_type = "icmp_type=%s," % 14
            elif port_range_min == 15:
                icmp_type = "icmp_type=%s," % 16
            elif port_range_min == 17:
                icmp_type = "icmp_type=%s," % 18
            elif port_range_min:
                icmp_type = "icmp_type=%s," % port_range_min

            if port_range_max:
                icmp_code = "icmp_code=%s," % port_range_max
        elif ip_proto == constants.PROTO_NAME_IPV6_ICMP:
            if port_range_min:
                icmp_type = "icmpv6_type=%s," % port_range_min
            if port_range_max:
                icmp_code = "icmpv6_code=%s," % port_range_max

        # Source and destination IPs.
        if eth_type == IPv4:
            ip_dst = "NXM_OF_IP_DST[]=NXM_OF_IP_SRC[],"
            ip_src = "NXM_OF_IP_SRC[]=NXM_OF_IP_DST[],"
        else:
            ip_dst = "NXM_NX_IPV6_DST[]=NXM_NX_IPV6_SRC[],"
            ip_src = "NXM_NX_IPV6_SRC[]=NXM_NX_IPV6_DST[],"

        # Learn action store table:
        if direction == INGRESS_DIRECTION:
            learn_action_table = OF_EGRESS_TABLE
            mac_match = "NXM_OF_ETH_SRC[]=NXM_OF_ETH_DST[],"
        elif direction == EGRESS_DIRECTION:
            learn_action_table = OF_ACCEPT_OR_INGRESS_TABLE
            mac_match = "NXM_OF_ETH_DST[]=NXM_OF_ETH_SRC[],"

        if cfg.CONF.AGENT.enable_learn_mac_match_all:
            mac_match = ("NXM_OF_ETH_SRC[]=NXM_OF_ETH_DST[],"
                         "NXM_OF_ETH_DST[]=NXM_OF_ETH_SRC[],")

        if ip_proto == constants.PROTO_NAME_TCP:
            learn_idle_timeout = self.learn_idle_timeout
            learn_hard_timeout = self.learn_hard_timeout
        else:
            learn_idle_timeout = LEARN_IDLE_TIMEOUT
            learn_hard_timeout = LEARN_HARD_TIMEOUT

        learn_actions = "learn(cookie=%(cookie)s," \
                        "table=%(table)s," \
                        "priority=%(priority)s," \
                        "idle_timeout=%(idle_timeout)s," \
                        "hard_timeout=%(hard_timeout)s," \
                        "%(proto)s," \
                        "%(mac_match)s" \
                        "%(ip_src)s" \
                        "%(ip_dst)s" \
                        "%(port_dst)s" \
                        "%(port_src)s" \
                        "%(icmp_type)s" \
                        "%(icmp_code)s" \
                        "NXM_OF_VLAN_TCI[0..11]," \
                        "load:NXM_NX_REG0[0..11]->NXM_OF_VLAN_TCI[0..11]," \
                        "output:NXM_OF_IN_PORT[])" % \
                        {'cookie': self.learn_cookie,
                         'table': learn_action_table,
                         'priority': priority,
                         'idle_timeout': learn_idle_timeout,
                         'hard_timeout': learn_hard_timeout,
                         'proto': proto_str,
                         'mac_match': mac_match,
                         'ip_src': ip_src,
                         'ip_dst': ip_dst,
                         'port_dst': port_dst_str,
                         'port_src': port_src_str,
                         'icmp_type': icmp_type,
                         'icmp_code': icmp_code}

        actions = self._process_ingress_resubmit_dscp_table(
            port, direction, ip_proto, vif_port, learn_actions)
        return actions

    def _process_ingress_resubmit_dscp_table(self, port, direction, ip_proto,
                                             vif_port, learn_actions):
        """
        To process dscp flows when port set dscp_learn attributes.
        """
        actions = "%s," % learn_actions
        if direction == EGRESS_DIRECTION:
            actions += "resubmit(,%s)" % OF_ACCEPT_OR_INGRESS_TABLE

        elif direction == INGRESS_DIRECTION:
            if ip_proto in [constants.PROTO_NAME_TCP,
                            constants.PROTO_NAME_UDP]:
                actions += self._process_resubmit_dscp_table(port)
            actions += "strip_vlan,output:%s" % vif_port.ofport
        return actions

    def _select_sg_rules_for_port(self, port):
        """Select rules from the security groups the port is member of."""
        port_sg_ids = port.get('security_groups', [])
        port_rules = []

        for sg_id in port_sg_ids:
            for rule in self.sg_rules.get(sg_id, []):
                port_rules.extend(
                    self._expand_sg_rule_with_remote_ips(rule, port))
        return port_rules

    def _write_flows_per_port_match(self, flow, port_match):
        if port_match == "" or isinstance(flow[port_match], int):
            self._add_flow(**flow)
        elif isinstance(flow[port_match], list):
            for portm in flow[port_match]:
                hp_flow = dict.copy(flow)
                hp_flow[port_match] = portm
                self._add_flow(**hp_flow)

    def _write_flows_per_ip(self, flow, rule, port, port_match, vif_port=None):
        """Write the needed flows per each IP in the port."""
        if not vif_port:
            vif_port = self._int_br.br.get_vif_port_by_id(port['device'])
            if not vif_port:
                LOG.warning("Port %(port_id)s not present in bridge. Skip "
                            "applying rules for this port", {'port_id': port})
                return

        ac_accept = ovsfw_consts.ACTION_ACCEPT
        ac_deny = ovsfw_consts.ACTION_DENY
        hp_flow = dict.copy(flow)
        prio = hp_flow['priority']
        # Write a rule(s) per ip.
        for fixed_ip in port['fixed_ips']:
            # Check if the rule and the IP address have the same version.
            if rule['ethertype'] != \
                    self._ip_version_from_address(fixed_ip):
                continue

            # If security group rule has no protoco, add TCP, UDP, ICMP rules
            # with high priority rule. Default IPv4/IPv6 has the lowest
            # priority.
            if not rule.get('protocol'):
                # Create high priority rules
                # for TCP and UDP protocols.
                for proto in [constants.PROTO_NAME_TCP,
                              constants.PROTO_NAME_UDP]:
                    # TCP/UDP has priority=1011
                    hp_flow['proto'] = self._write_proto(rule['ethertype'],
                                                         proto)
                    hp_flow['priority'] = prio + 1
                    hp_flow['actions'] = self._get_learn_action_rule(
                        rule['direction'],
                        OF_LEARNED_HIGH_PRIO,
                        "",
                        "",
                        rule['ethertype'],
                        proto,
                        vif_port) if \
                        rule.get("action") != ac_deny else "drop"
                    self._write_flows_per_port_match(hp_flow, port_match)

                # ICMP has priority=1012
                hp_flow['proto'] = self._write_proto(
                    rule['ethertype'], constants.PROTO_NAME_ICMP)
                hp_flow['priority'] = prio + 2
                hp_flow['actions'] = self._get_learn_action_rule(
                    rule['direction'],
                    OF_LEARNED_HIGH_PRIO + 2,
                    rule.get('port_range_min'),
                    rule.get('port_range_max'),
                    rule['ethertype'],
                    constants.PROTO_NAME_ICMP,
                    vif_port) if \
                    rule.get("action", ac_accept) != ac_deny else "drop"
                self._write_flows_per_port_match(hp_flow, port_match)

                # Default protocol: "ip/ipv6".
                # Default IPv4/IPv6 has priority=1010
                # Write normal "learn action" for every flow.
                flow['actions'] = self._get_learn_action_rule(
                    rule['direction'],
                    OF_LEARNED_LOW_PRIO,
                    rule.get('port_range_min'),
                    rule.get('port_range_max'),
                    rule['ethertype'],
                    rule.get('protocol'),
                    vif_port) if \
                    rule.get("action") != ac_deny else "drop"
                self._write_flows_per_port_match(flow, port_match)
            else:
                # Rule has specific protocol will have priority=1013
                # Write normal "learn action" for every flow.
                flow['priority'] += 3
                flow['actions'] = self._get_learn_action_rule(
                    rule['direction'],
                    OF_LEARNED_LOW_PRIO + 3,
                    rule.get('port_range_min'),
                    rule.get('port_range_max'),
                    rule['ethertype'],
                    rule.get('protocol'),
                    vif_port,
                    port) if \
                    rule.get("action", ac_accept) != ac_deny else "drop"
                self._write_flows_per_port_match(flow, port_match)

        # Write multicast rule.
        self._write_multicast_flow(flow, rule['direction'], port,
                                   port_match, OF_LEARNED_LOW_PRIO,
                                   rule['ethertype'], rule.get('protocol'))

    def _add_rules_flows(self, port, vif_port):
        rules = self._select_sg_rules_for_port(port)
        for rule in rules:
            ethertype = rule['ethertype']
            direction = rule['direction']
            protocol = rule.get('protocol')
            port_range_min = rule.get('port_range_min')
            port_range_max = rule.get('port_range_max')
            source_ip_prefix = rule.get('source_ip_prefix')
            dest_ip_prefix = rule.get('dest_ip_prefix')
            priority = int(rule.get('priority', 1)) * PRIORITY_GAP

            flow = {}
            # Direction.
            if direction == EGRESS_DIRECTION:
                flow['priority'] = min(
                    OF_EGRESS_PORT_RULE_PRIO + priority,
                    OF_EGRESS_ANTISPOOF_PRIO - 10)
                flow['table'] = OF_EGRESS_TABLE
                flow["dl_src"] = port["mac_address"]
                flow["dl_vlan"] = port['vinfo']['tag']

            elif direction == INGRESS_DIRECTION:
                flow['priority'] = min(
                    OF_INGRESS_PORT_RULE_PRIO + priority,
                    OF_INGRESS_ANTISPOOF_PRIO - 10)
                flow['table'] = OF_ACCEPT_OR_INGRESS_TABLE
                flow["dl_vlan"] = port['vinfo']['tag']
                flow["dl_dst"] = port["mac_address"]

            # Protocol.
            flow['proto'] = self._write_proto(ethertype, protocol)

            # Port range.
            port_match = ""
            if (port_range_min and port_range_max and
                    protocol in [constants.PROTO_NAME_TCP,
                                 constants.PROTO_NAME_UDP,
                                 constants.PROTO_NAME_SCTP]):
                # SCTP(Stream Control Transmission Protocol) use tcp_dst field
                # to filter port range.
                if protocol == constants.PROTO_NAME_SCTP:
                    port_match = "sctp_dst"
                else:
                    port_match = "%s_dst" % protocol
                if port_range_max > port_range_min:
                    flow[port_match] = neutron_utils.port_rule_masking(
                        port_range_min,
                        port_range_max)
                else:
                    flow[port_match] = int(port_range_min)

            # Destination and source address.
            if dest_ip_prefix and dest_ip_prefix != "0.0.0.0/0":
                flow[OF_MNEMONICS[ethertype]["ip_dst"]] = dest_ip_prefix

            if source_ip_prefix and source_ip_prefix != "0.0.0.0/0":
                flow[OF_MNEMONICS[ethertype]["ip_src"]] = source_ip_prefix

            # Process allowed address pair that could be used for security
            # group rules.
            flows = [flow]
            for addr_pair in port.get("allowed_address_pairs", []):
                mac = addr_pair.get("mac_address", None)
                if not mac:
                    continue
                flow_allowed = copy.copy(flow)
                if direction == EGRESS_DIRECTION:
                    flow_allowed["dl_src"] = mac
                elif direction == INGRESS_DIRECTION:
                    flow_allowed["dl_dst"] = mac
                flows.append(flow_allowed)

            # Write flow.
            for flow in flows:
                self._write_flows_per_ip(flow, rule, port, port_match,
                                         vif_port)

    def _apply_flows(self):
        self._int_br.apply_flows()

    def prepare_port_filter(self, port):
        LOG.debug("OFW Preparing device (%s) filter: %s", port['device'],
                  port)
        self.iptables_helper.cleanup_port(port)
        vif_port = self._int_br.br.get_vif_port_by_id(port['device'])
        if not vif_port:
            LOG.warning("Port %(port_id)s not present in bridge. Skip "
                        "applying rules for this port", {'port_id': port})
            return

        try:
            port['vinfo'] = self._vif_port_info(vif_port.port_name)
        except exceptions.OVSFWTagNotFound as not_found_e:
            LOG.warning("Port %(port_id)s tag does not "
                        "exist in ovsdb other_config: %(err)s.",
                        {'port_id': port['device'],
                         'err': not_found_e})
            return
        self._remove_flows(port, vif_port)

        if not firewall.port_sec_enabled(port):
            self._initialize_egress_no_port_security(port, vif_port)
            self._initialize_dscp_learn_no_port_security(port, vif_port)
            return

        self._filtered_ports[port['device']] = port
        self._filtered_in_ports[port['device']] = vif_port.ofport
        self._add_base_flows(port, vif_port)
        self._add_rules_flows(port, vif_port)

    def _remove_egress_no_port_security(self, port_id):
        try:
            port, ovs_port = self.unfiltered[port_id]
        except KeyError:
            raise exceptions.OVSFWPortNotHandled(port_id=port_id)

        vlan_tag = port['vinfo']['tag']

        self._del_flows(
            table=OF_ZERO_TABLE,
            in_port=ovs_port.ofport,
            proto='arp',
        )
        if port['vinfo']["network_type"] == constants.TYPE_VLAN:
            for icmpv6_type in IPV6_ND_MESSAGES_NO_NS:
                self._del_flows(table=OF_ZERO_TABLE,
                                proto=self._write_proto(
                                    IPv6, constants.PROTO_NAME_IPV6_ICMP),
                                icmpv6_type=icmpv6_type,
                                dl_vlan=port['vinfo']['segmentation_id'],
                                dl_dst=ovs_port.vif_mac)
            for ip6_addr in self._get_port_ipv6_addresses(port, ovs_port):
                self._del_flows(table=OF_ZERO_TABLE,
                                proto=self._write_proto(
                                    IPv6, constants.PROTO_NAME_IPV6_ICMP),
                                icmpv6_type=ICMPv6_TYPE_NS,
                                dl_vlan=port['vinfo']['segmentation_id'],
                                nd_target=ip6_addr)
        self._del_flows(
            table=OF_ZERO_TABLE,
            in_port=ovs_port.ofport,
        )
        self._del_flows(
            table=OF_TRANSIENT_TABLE,
            in_port=ovs_port.ofport
        )
        if port['vinfo']["network_type"] == constants.TYPE_VLAN:
            for fixed_ip in port['fixed_ips']:
                if self._ip_version_from_address(fixed_ip) == IPv4:
                    self._del_flows(
                        table=OF_TRANSIENT_TABLE,
                        proto=self._write_proto(IPv4),
                        dl_vlan=vlan_tag,
                        nw_dst=fixed_ip,
                        dl_dst=ovs_port.vif_mac)
                    self._del_flows(
                        table=OF_SEC_EXT_TABLE,
                        proto=self._write_proto(IPv4),
                        dl_vlan=port['vinfo']['tag'],
                        nw_dst=fixed_ip,
                        dl_dst=ovs_port.vif_mac)
                else:
                    self._del_flows(
                        table=OF_TRANSIENT_TABLE,
                        proto=self._write_proto(IPv6),
                        dl_vlan=port['vinfo']['tag'],
                        ipv6_dst=fixed_ip,
                        dl_dst=ovs_port.vif_mac)
                    self._del_flows(
                        table=OF_SEC_EXT_TABLE,
                        proto=self._write_proto(IPv6),
                        dl_vlan=port['vinfo']['tag'],
                        ipv6_dst=fixed_ip,
                        dl_dst=ovs_port.vif_mac)
        self._del_flows(
            table=OF_SELECT_TABLE,
            reg_port=ovs_port.ofport,
            reg_net=vlan_tag,
        )
        self._del_flows(
            table=OF_SELECT_TABLE,
            dl_vlan=vlan_tag,
            dl_dst=ovs_port.vif_mac,
        )
        self._del_flows(
            table=OF_ACCEPT_OR_INGRESS_TABLE,
            reg_port=ovs_port.ofport
        )
        self._del_flows(
            table=OF_SEC_EXT_TABLE,
            reg_port=ovs_port.ofport,
        )

        self.delete_accepted_egress_direct_flow(
            ovs_port.vif_mac, vlan_tag)

        self._delete_direct_flows_no_port_security(port, ovs_port)
        del self.unfiltered[port_id]

    def _delete_direct_flows_no_port_security(self, port, ovs_port=None):
        if not port or port['vinfo']["network_type"] != constants.TYPE_VLAN:
            return
        dl_dst = ovs_port.vif_mac if ovs_port else port['mac_address']
        for icmpv6_type in IPV6_ND_MESSAGES_NO_NS:
            self._del_flows(table=OF_ZERO_TABLE,
                            proto=self._write_proto(
                                IPv6, constants.PROTO_NAME_IPV6_ICMP),
                            icmpv6_type=icmpv6_type,
                            dl_vlan=port['vinfo']['segmentation_id'],
                            dl_dst=dl_dst)
        for ip6_addr in self._get_port_ipv6_addresses(port, ovs_port):
            self._del_flows(table=OF_ZERO_TABLE,
                            proto=self._write_proto(
                                IPv6, constants.PROTO_NAME_IPV6_ICMP),
                            icmpv6_type=ICMPv6_TYPE_NS,
                            dl_vlan=port['vinfo']['segmentation_id'],
                            nd_target=ip6_addr)
        for fixed_ip in port['fixed_ips']:
            if self._ip_version_from_address(fixed_ip) == IPv4:
                self._del_flows(
                    table=OF_TRANSIENT_TABLE,
                    proto=self._write_proto(IPv4),
                    dl_vlan=port['vinfo']['tag'],
                    nw_dst=fixed_ip,
                    dl_dst=dl_dst)
                self._del_flows(
                    table=TRAFFIC_MIRROR_TABLE,
                    proto=self._write_proto(IPv4),
                    dl_vlan=port['vinfo']['tag'],
                    nw_dst=fixed_ip,
                    dl_dst=dl_dst)
                self._del_flows(
                    table=OF_SEC_EXT_TABLE,
                    proto=self._write_proto(IPv4),
                    dl_vlan=port['vinfo']['tag'],
                    nw_dst=fixed_ip,
                    dl_dst=dl_dst)
            else:
                self._del_flows(
                    table=OF_TRANSIENT_TABLE,
                    proto=self._write_proto(IPv6),
                    dl_vlan=port['vinfo']['tag'],
                    ipv6_dst=fixed_ip,
                    dl_dst=dl_dst)
                self._del_flows(
                    table=TRAFFIC_MIRROR_TABLE,
                    proto=self._write_proto(IPv6),
                    dl_vlan=port['vinfo']['tag'],
                    ipv6_dst=fixed_ip,
                    dl_dst=dl_dst)
                self._del_flows(
                    table=OF_SEC_EXT_TABLE,
                    proto=self._write_proto(IPv6),
                    dl_vlan=port['vinfo']['tag'],
                    ipv6_dst=fixed_ip,
                    dl_dst=dl_dst)

    def is_port_managed(self, port):
        return port['device'] in self._filtered_ports

    def _initialize_dscp_learn_no_port_security(self, port, vif_port):
        if not cfg.CONF.AGENT.enable_lbaas_dscp:
            return
        port_attrs = port.get('cloud_attributes')
        if port_attrs and "dscp_learn" in \
                port_attrs['cloud_attrs']:
            self._add_flow(
                table=OF_TRANSIENT_TABLE,
                priority=100,
                in_port=vif_port.ofport,
                actions='set_field:%d->reg%d,'
                        'set_field:%d->reg%d,'
                        'mod_vlan_vid:%d,'
                        'resubmit(,%d)' % (
                            vif_port.ofport,
                            ovsfw_consts.REG_PORT,
                            port['vinfo']['tag'],
                            ovsfw_consts.REG_NET,
                            port['vinfo']['tag'],
                            TRAFFIC_MIRROR_TABLE)
            )

    def update_port_filter(self, port):
        LOG.debug("OFW Updating device (%s) filter: %s", port['device'],
                  port)

        old_port = self._filtered_ports.get(port['device'])
        vif_port = self._int_br.br.get_vif_port_by_id(port['device'])
        if not vif_port:
            LOG.warning("Port %(port_id)s not present in bridge. Skip "
                        "applying rules for this port", {'port_id': port})
            return

        try:
            port['vinfo'] = self._vif_port_info(vif_port.port_name)
        except exceptions.OVSFWTagNotFound as not_found_e:
            LOG.warning("Port %(port_id)s tag does not "
                        "exist in ovsdb other_config: %(err)s.",
                        {'port_id': port['device'],
                         'err': not_found_e})
            return

        if old_port:
            self._remove_flows(old_port, vif_port)

        if not firewall.port_sec_enabled(port):
            self.remove_port_filter(port, vif_port)
            self._initialize_egress_no_port_security(port, vif_port)
            self._initialize_dscp_learn_no_port_security(port, vif_port)
            return
        elif not self.is_port_managed(port):
            try:
                if not old_port and (port['device'] not in self.unfiltered):
                    self.unfiltered[port['device']] = (port, vif_port)
                self._remove_egress_no_port_security(port['device'])
            except exceptions.OVSFWPortNotHandled as e:
                LOG.debug(e)
            else:
                self.prepare_port_filter(port)
                return
        if port['device'] not in self._filtered_ports:
            LOG.info('Attempted to update port filter which is not '
                     'filtered %s', port['device'])
            return

        self._filtered_ports[port['device']] = port
        self._filtered_in_ports[port['device']] = vif_port.ofport
        self._add_base_flows(port, vif_port)
        self._add_rules_flows(port, vif_port)

    def remove_trusted_ports(self, port_ids):
        for port_id in port_ids:
            try:
                self._remove_egress_no_port_security(port_id)
            except exceptions.OVSFWPortNotHandled as e:
                LOG.debug(e)

    def remove_port_filter(self, port, vif_port=None):
        LOG.debug("OFW Removing device (%s) filter: %s", port['device'],
                  port)
        if not self._filtered_ports.get(port['device']):
            LOG.info('Attempted to remove port filter which is not '
                     'filtered %r', port)
            return
        self._remove_flows(port, vif_port)
        self._filtered_ports.pop(port['device'])
        self._filtered_in_ports.pop(port['device'])

    def filter_defer_apply_on(self):
        LOG.debug("OFW defer_apply_on")
        self._deferred = True

    def filter_defer_apply_off(self):
        LOG.debug("OFW defer_apply_off")
        if self._deferred:
            self._apply_flows()
            self._deferred = False

    @staticmethod
    def _ip_version_from_address(ip_string):
        ip_net = netaddr.IPNetwork(ip_string)
        if ip_net.version == constants.IP_VERSION_4:
            return IPv4
        if ip_net.version == constants.IP_VERSION_6:
            return IPv6
        raise ValueError('Illegal IP string address')
