#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.


from neutron.common import utils
from neutron.db.models import traffic_mirror as models
from neutron.objects import base
from neutron.objects import common_types
from oslo_versionedobjects import fields as obj_fields


@base.NeutronObjectRegistry.register
class TrafficMirrorSession(base.NeutronDbObject):
    VERSION = '1.0'

    db_model = models.TrafficMirrorSession

    fields = {
        'id': obj_fields.UUIDField(),
        'project_id': obj_fields.StringField(),
        'name': obj_fields.StringField(nullable=True),
        'traffic_mirror_filter_id': obj_fields.UUIDField(nullable=False),
        'traffic_mirror_target_port_id': obj_fields.UUIDField(nullable=False),
        'traffic_mirror_target_type': obj_fields.StringField(nullable=False),
        'virtual_network_id': obj_fields.IntegerField(nullable=True),
        'packet_length': obj_fields.IntegerField(nullable=True),
        'priority': obj_fields.IntegerField(nullable=False),
        'enabled': obj_fields.BooleanField(nullable=False),
        'traffic_mirror_sources': obj_fields.ListOfObjectsField(
            'TrafficMirrorSourceBinding', nullable=True),
        'segmentation_id': obj_fields.IntegerField(nullable=False),
    }

    fields_no_update = ['project_id', 'id']
    synthetic_fields = ['traffic_mirror_sources', 'segmentation_id']
    fields_need_translation = {'traffic_mirror_sources': 'sources'}

    foreign_keys = {'TrafficMirrorFilter': {'traffic_mirror_filter_id': 'id'}}

    def from_db_object(self, db_obj):
        super(TrafficMirrorSession, self).from_db_object(db_obj)

        session_seg = TrafficMirrorSessionSegment.get_object(
            self.obj_context, traffic_mirror_session_id=self.id)
        if session_seg:
            setattr(self, 'segmentation_id', session_seg.segmentation_id)
            self.obj_reset_changes(['segmentation_id'])


@base.NeutronObjectRegistry.register
class TrafficMirrorFilter(base.NeutronDbObject):
    VERSION = '1.0'

    db_model = models.TrafficMirrorFilter

    fields = {
        'id': obj_fields.UUIDField(),
        'project_id': obj_fields.StringField(),
        'name': obj_fields.StringField(nullable=True),
        'ingress_rules': obj_fields.ListOfObjectsField(
            'TrafficMirrorFilterRule', nullable=True),
        'egress_rules': obj_fields.ListOfObjectsField(
            'TrafficMirrorFilterRule', nullable=True),
        'rules': obj_fields.ListOfObjectsField(
            'TrafficMirrorFilterRule', nullable=True),
    }

    fields_no_update = ['project_id']
    synthetic_fields = ['ingress_rules', 'egress_rules', 'rules']
    lazy_fields = set(['rules'])

    def from_db_object(self, db_obj):
        super(TrafficMirrorFilter, self).from_db_object(db_obj)

        ingress_rules = [r for r in self.rules if r.direction == 'ingress']
        egress_rules = [r for r in self.rules if r.direction == 'egress']
        setattr(self, 'ingress_rules', ingress_rules)
        setattr(self, 'egress_rules', egress_rules)
        self.obj_reset_changes(['ingress_rules', 'egress_rules'])


@base.NeutronObjectRegistry.register
class TrafficMirrorFilterRule(base.NeutronDbObject):
    VERSION = '1.0'

    db_model = models.TrafficMirrorFilterRule

    fields = {
        'id': obj_fields.UUIDField(),
        'project_id': obj_fields.StringField(),
        'traffic_mirror_filter_id': obj_fields.UUIDField(),
        'direction': common_types.FlowDirectionEnumField(nullable=False),
        'ethertype': common_types.EtherTypeEnumField(nullable=False),
        'protocol': common_types.IpProtocolEnumField(nullable=True),
        'src_cidr': common_types.IPNetworkField(nullable=True),
        'dst_cidr': common_types.IPNetworkField(nullable=True),
        'src_port_range_min': common_types.PortRangeWith0Field(nullable=True),
        'src_port_range_max': common_types.PortRangeWith0Field(nullable=True),
        'dst_port_range_min': common_types.PortRangeWith0Field(nullable=True),
        'dst_port_range_max': common_types.PortRangeWith0Field(nullable=True),
        'action':
            common_types.TrafficMirrorFilterRuleActionField(nullable=False),
        'priority': obj_fields.IntegerField(nullable=False)
    }

    foreign_keys = {'TrafficMirrorFilter': {'traffic_mirror_filter_id': 'id'}}

    @classmethod
    def modify_fields_to_db(cls, fields):
        result = super(
            TrafficMirrorFilterRule, cls).modify_fields_to_db(fields)
        src_cidr = result.get('src_cidr')
        dst_cidr = result.get('dst_cidr')
        if src_cidr:
            result['src_cidr'] = cls.filter_to_str(src_cidr)
        if dst_cidr:
            result['dst_cidr'] = cls.filter_to_str(dst_cidr)
        return result

    @classmethod
    def modify_fields_from_db(cls, db_obj):
        fields = super(
            TrafficMirrorFilterRule, cls).modify_fields_from_db(db_obj)
        if 'src_cidr' in fields:
            fields['src_cidr'] = (
                utils.AuthenticIPNetwork(fields['src_cidr']))
        if 'dst_cidr' in fields:
            fields['dst_cidr'] = (
                utils.AuthenticIPNetwork(fields['dst_cidr']))
        return fields


@base.NeutronObjectRegistry.register
class TrafficMirrorSourceBinding(base.NeutronDbObject):
    VERSION = '1.0'

    db_model = models.TrafficMirrorSourceBinding

    primary_keys = ['traffic_mirror_session_id', 'source_port_id']

    fields = {
        'traffic_mirror_session_id': obj_fields.UUIDField(),
        'source_port_id': obj_fields.UUIDField(),
    }

    foreign_keys = {
        'TrafficMirrorSession': {'traffic_mirror_session_id': 'id'}}


@base.NeutronObjectRegistry.register
class TrafficMirrorSessionSegment(base.NeutronDbObject):
    VERSION = '1.0'

    db_model = models.TrafficMirrorSessionSegment

    primary_keys = ['traffic_mirror_session_id']

    fields = {
        'traffic_mirror_session_id': obj_fields.UUIDField(),
        'segmentation_id': obj_fields.IntegerField(),
    }

    foreign_keys = {
        'TrafficMirrorSession': {'traffic_mirror_session_id': 'id'}}


@base.NeutronObjectRegistry.register
class MirrorTunnelNetwork(base.NeutronDbObject):
    VERSION = '1.0'

    db_model = models.MirrorTunnelNetwork

    primary_keys = ['availability_zone']

    fields = {
        'network_id': obj_fields.UUIDField(),
        'availability_zone': obj_fields.StringField(),
    }
